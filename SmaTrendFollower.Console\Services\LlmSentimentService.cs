using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Json;
using System.Text.Json.Nodes;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for performing sentiment analysis using LLM providers (OpenAI or Gemini).
/// Used as an overlay on top of FinBERT for low-confidence cases.
/// </summary>
internal sealed class LlmSentimentService
{
    private readonly IHttpClientFactory _http;
    private readonly LlmSentimentOptions _opt;
    private readonly ILogger<LlmSentimentService> _log;

    /// <summary>
    /// Initializes the LLM sentiment service with HTTP client factory and configuration.
    /// </summary>
    /// <param name="http">HTTP client factory for API calls</param>
    /// <param name="opt">LLM sentiment configuration options</param>
    /// <param name="log">Logger for service operations</param>
    public LlmSentimentService(IHttpClientFactory http,
                               IOptions<LlmSentimentOptions> opt,
                               ILogger<LlmSentimentService> log)
    {
        _http = http ?? throw new ArgumentNullException(nameof(http));
        _opt = opt?.Value ?? throw new ArgumentNullException(nameof(opt));
        _log = log ?? throw new ArgumentNullException(nameof(log));
    }

    /// <summary>
    /// Gets sentiment score from the configured LLM provider.
    /// Returns null if the call fails or times out.
    /// </summary>
    /// <param name="text">Text to analyze for sentiment</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Sentiment score (-1.0 to ****) or null if failed</returns>
    public async Task<double?> GetSentimentAsync(string text, CancellationToken ct = default)
    {
        try
        {
            return _opt.Provider switch
            {
                "OpenAI" => await QueryOpenAiAsync(text, ct),
                "Gemini" => await QueryGeminiAsync(text, ct),
                _ => null
            };
        }
        catch (Exception ex)
        {
            _log.LogDebug(ex, "LLM sentiment call failed for provider {Provider}", _opt.Provider);
            return null;
        }
    }

    /// <summary>
    /// Queries OpenAI API for sentiment analysis.
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Sentiment score or null if failed</returns>
    private async Task<double?> QueryOpenAiAsync(string text, CancellationToken ct)
    {
        var body = new
        {
            model = _opt.Model,
            temperature = 0,
            messages = new[]
            {
                new { role = "system", content = "Return JSON {\"label\":\"positive|negative|neutral\",\"score\":0-1} only." },
                new { role = "user", content = text.Length > 400 ? text[..400] : text }
            }
        };

        var cli = _http.CreateClient("OpenAI");
        var res = await cli.PostAsJsonAsync("/v1/chat/completions", body, ct);
        res.EnsureSuccessStatusCode();

        var json = await res.Content.ReadFromJsonAsync<JsonObject>(cancellationToken: ct);
        var msg = json?["choices"]?[0]?["message"]?["content"]?.GetValue<string>() ?? "";
        
        if (string.IsNullOrWhiteSpace(msg))
        {
            _log.LogWarning("OpenAI returned empty response");
            return null;
        }

        var doc = JsonNode.Parse(msg);
        if (doc == null)
        {
            _log.LogWarning("Failed to parse OpenAI response as JSON: {Response}", msg);
            return null;
        }

        var label = doc["label"]?.GetValue<string>();
        var score = doc["score"]?.GetValue<double>() ?? 0.0;

        return label switch
        {
            "positive" => score,
            "negative" => -score,
            _ => 0.0
        };
    }

    /// <summary>
    /// Queries Gemini API for sentiment analysis.
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Sentiment score or null if failed</returns>
    private async Task<double?> QueryGeminiAsync(string text, CancellationToken ct)
    {
        var body = new
        {
            contents = new[] { new { parts = new[] { new { text } } } },
            generationConfig = new { temperature = 0 }
        };

        var cli = _http.CreateClient("Gemini");
        var res = await cli.PostAsJsonAsync($"/v1beta/models/{_opt.Model}:generateContent", body, ct);
        res.EnsureSuccessStatusCode();

        var json = await res.Content.ReadFromJsonAsync<JsonObject>(cancellationToken: ct);
        var msg = json?["candidates"]?[0]?["content"]?["parts"]?[0]?["text"]?.GetValue<string>() ?? "";
        
        if (string.IsNullOrWhiteSpace(msg))
        {
            _log.LogWarning("Gemini returned empty response");
            return null;
        }

        var doc = JsonNode.Parse(msg);
        if (doc == null)
        {
            _log.LogWarning("Failed to parse Gemini response as JSON: {Response}", msg);
            return null;
        }

        var label = doc["label"]?.GetValue<string>();
        var score = doc["score"]?.GetValue<double>() ?? 0.0;

        return label switch
        {
            "positive" => score,
            "negative" => -score,
            _ => 0.0
        };
    }
}
